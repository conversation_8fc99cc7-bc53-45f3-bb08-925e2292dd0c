<template>
  <div class="user-list-container">
    <!-- 控制面板 -->
    <div class="control-panel">
      <el-button 
        :type="userPolling.isRunning ? 'danger' : 'primary'"
        @click="togglePolling"
      >
        {{ userPolling.isRunning ? '停止轮询' : '开始轮询' }}
      </el-button>
      
      <el-button @click="userPolling.refresh()">
        手动刷新
      </el-button>
      
      <el-input-number
        v-model="pollingInterval"
        :min="500"
        :max="10000"
        :step="500"
        @change="updateInterval"
        style="width: 150px; margin-left: 10px;"
      />
      <span style="margin-left: 5px;">毫秒</span>
      
      <el-tag 
        :type="userPolling.isRunning ? 'success' : 'info'"
        style="margin-left: 10px;"
      >
        {{ userPolling.isRunning ? '轮询中' : '已停止' }}
      </el-tag>
    </div>

    <!-- 数据统计 -->
    <div class="stats-panel">
      <el-card>
        <div class="stats-content">
          <div class="stat-item">
            <span class="label">总记录数:</span>
            <span class="value">{{ userPolling.total }}</span>
          </div>
          <div class="stat-item">
            <span class="label">当前页:</span>
            <span class="value">{{ userPolling.queryParams.pageNo }}</span>
          </div>
          <div class="stat-item">
            <span class="label">每页条数:</span>
            <span class="value">{{ userPolling.queryParams.pageSize }}</span>
          </div>
          <div class="stat-item">
            <span class="label">状态:</span>
            <el-tag :type="userPolling.loading ? 'warning' : 'success'">
              {{ userPolling.loading ? '加载中' : '已完成' }}
            </el-tag>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 错误提示 -->
    <el-alert
      v-if="userPolling.error"
      :title="userPolling.error.message"
      type="error"
      :closable="false"
      style="margin: 10px 0;"
    />

    <!-- 数据表格 -->
    <el-table
      :data="userPolling.data"
      :loading="userPolling.loading"
      border
      style="width: 100%"
    >
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="username" label="用户名" />
      <el-table-column prop="nickname" label="昵称" />
      <el-table-column prop="email" label="邮箱" />
      <el-table-column prop="mobile" label="手机号" />
      <el-table-column prop="status" label="状态">
        <template #default="{ row }">
          <el-tag :type="row.status === 0 ? 'success' : 'danger'">
            {{ row.status === 0 ? '正常' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" />
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="userPolling.queryParams.pageNo"
      v-model:page-size="userPolling.queryParams.pageSize"
      :total="userPolling.total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      style="margin-top: 20px; text-align: right;"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onUnmounted } from 'vue'
import { UserDataPolling } from '@/utils/UserDataPolling'

/** 用户列表轮询组件 */
defineOptions({ name: 'UserListWithPolling' })

// 轮询间隔控制
const pollingInterval = ref(1000)

// 创建用户数据轮询实例
const userPolling = new UserDataPolling(
  {
    pageNo: 1,
    pageSize: 10
  },
  {
    interval: pollingInterval.value,
    immediate: true,
    autoStart: true
  }
)

/**
 * 切换轮询状态
 */
const togglePolling = () => {
  if (userPolling.isRunning) {
    userPolling.stop()
  } else {
    userPolling.start()
  }
}

/**
 * 更新轮询间隔
 */
const updateInterval = (newInterval: number) => {
  userPolling.setInterval(newInterval)
}

/**
 * 处理每页条数变化
 */
const handleSizeChange = (newSize: number) => {
  userPolling.updateParams({
    pageSize: newSize,
    pageNo: 1 // 重置到第一页
  })
}

/**
 * 处理当前页变化
 */
const handleCurrentChange = (newPage: number) => {
  userPolling.updateParams({
    pageNo: newPage
  })
}

// 组件卸载时清理资源
onUnmounted(() => {
  userPolling.destroy()
})
</script>

<style scoped>
.user-list-container {
  padding: 20px;
}

.control-panel {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.stats-panel {
  margin-bottom: 20px;
}

.stats-content {
  display: flex;
  gap: 30px;
  align-items: center;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.stat-item .label {
  font-weight: 500;
  color: #666;
}

.stat-item .value {
  font-weight: bold;
  color: #333;
}
</style>
