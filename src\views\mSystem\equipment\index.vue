<script setup lang="ts">
import CommonTitle from '@/components/commonTitle/commonTitle.vue'
import {Search, Plus, RefreshLeft, Edit, Delete} from '@element-plus/icons-vue'

const options = [
  {
    value: 'Option1',
    label: 'Option1'
  },
  {
    value: 'Option2',
    label: 'Option2'
  },
  {
    value: 'Option3',
    label: 'Option3'
  },
  {
    value: 'Option4',
    label: 'Option4'
  },
  {
    value: 'Option5',
    label: 'Option5'
  }
]

const titleData = [
  {
    label: '编号',
    prop: 'id'
  },
  {
    label: '设备代码',
    prop: 'code'
  },
  {
    label: '设备名称',
    prop: 'name'
  },
  {
    label: '设备种类',
    prop: 'type'
  },
  {
    label: '设备ip',
    prop: 'ip'
  },
  {
    label: '设备地址',
    prop: 'address'
  }
]

const tableDataList = [
  {
    id: '1',
    code: '1',
    name: '1',
    type: '1',
    ip: '1',
    address: '1'
  }
]

</script>

<template>
  <div class="contant">
    <CommonTitle text="管理设备" />

    <div style="margin-top: 20px">
      <el-select v-model="value" placeholder="Select" size="large" style="width: 176px">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-input
        placeholder="请输入内容"
        style="width: 294px; margin-left: 10px; min-height: 40px"
      />
      <el-button :icon="Search" class="equipButton" style="border: 1px solid rgba(23, 178, 212, 1)"> 搜索</el-button>
      <el-button :icon="RefreshLeft" class="equipButton" style="border: 1px solid rgba(234, 172, 99, 1)"> 重置</el-button>
      <el-button :icon="Plus" class="equipButton" style="border: 1px solid rgba(38, 195, 120, 1)"> 添加设备</el-button>
    </div>



    <el-table :data="tableDataList" class="table" height="600px">
      <el-table-column
        :prop="item.prop"
        :label="item.label"
        v-for="(item, index) in titleData"
        :key="index"
      />
      <el-table-column label="操作" align="center">
        <template #default>
          <el-button
            type="primary"
            link
          >修改
          </el-button>
          <el-button
            type="danger"
            link
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="page">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="[100, 200, 300, 400]"
        size="large"
        layout="total, sizes, prev, pager, next, jumper"
        :total="400"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>


  </div>
</template>

<style scoped lang="scss">
.contant {
  margin-top: 17px;
  margin-left: 34px;
  margin-right: 20px;
}

.equipButton {
  margin-left: 10px;
  min-height: 40px;
}


.page {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}
</style>
